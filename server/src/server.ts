import { Hono } from "hono";
import { setupMiddleware } from "./middleware";
import { featuresApp } from "./features";
import { systemRoutes } from "./features/system";

/**
 * Create and configure the Hono application
 */
export function createApp(): Hono {
  const app = new Hono();

  // Setup all middleware
  setupMiddleware(app);

  // System routes (no prefix) - /, /health, /metrics
  app.route("/", systemRoutes);

  // API routes with /api prefix
  app.route("/api", featuresApp);

  return app;
}
