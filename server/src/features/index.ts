import { Hono } from "hono";
import { userRoutes } from "./user";
import { systemRoutes } from "./system";
import { voucher } from "./voucher";

/**
 * Features Module
 * Centralized routing for all application features
 * Maintains Hono RPC chaining compatibility
 */
const featuresApp = new Hono()
  // System routes - / (root), /health/*, /metrics
  .route("/", systemRoutes)

  // User routes - /user/*
  .route("/user", userRoutes)

  // Voucher routes - /voucher/*
  .route("/voucher", voucher);

// Export the combined features app
export { featuresApp };

// Export individual feature routes for direct access if needed
export { systemRoutes } from "./system";
export { userRoutes } from "./user";
export { voucher } from "./voucher";
