import { Hono } from "hono";
import { userRoutes } from "./user";
import { voucher } from "./voucher/index";

/**
 * Features Module
 * Centralized routing for all application features
 * Maintains Hono RPC chaining compatibility
 */
const featuresApp = new Hono()
  // User routes - /user/*
  .route("/user", userRoutes)

  // Voucher routes - /voucher/*
  .route("/voucher", voucher);

// Export the combined features app
export { featuresApp };

// Export individual feature routes for direct access if needed
export { userRoutes } from "./user";
export { voucher } from "./voucher";
