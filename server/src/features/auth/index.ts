import { Hono } from "hono";
import { auth } from "./authentication";
import { verification } from "./verification";

/**
 * Auth routes module
 * Combines all authentication-related routes
 * These are public/semi-public operations that don't require user to be logged in
 */
const authRoutes = new Hono()
  // Authentication routes - /api/auth/*
  .route("/", auth)

  // Verification routes - /api/auth/*
  .route("/", verification);

// Export the combined auth routes
export { authRoutes };

// Export individual modules for direct access if needed
export { auth } from "./authentication";
export { verification } from "./verification";

// Export service functions for use in other modules
export {
  createVerificationRecord,
} from "./verification";
