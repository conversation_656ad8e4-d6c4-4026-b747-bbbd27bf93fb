import { Hono } from "hono";
import { env } from "../../config/env";
import { sendSuccess } from "../../utils/response";
import { API_CONSTANTS } from "shared/dist";

/**
 * System Routes
 * Provides API information, health checks, and system status
 * Combines API info and health monitoring in one feature
 */
export const systemRoutes = new Hono()
  // Root API info - GET /
  .get("/", (c) => {
    return sendSuccess(
      c,
      {
        name: "Takono API",
        version: API_CONSTANTS.VERSION,
        environment: env.NODE_ENV,
        timestamp: new Date().toISOString(),
        description: "Takono API - Business Management Platform",
        endpoints: {
          health: "/health",
          healthReady: "/health/ready",
          healthLive: "/health/live",
          userAuth: "/api/user/auth",
          userSessions: "/api/user/sessions",
          userVerification: "/api/user/verification",
          voucher: "/api/voucher",
          docs: "/docs", // Future: API documentation
        },
        status: "operational",
      },
      "Welcome to Takono API"
    );
  })

  // Health check - GET /health
  .get("/health", (c) => {
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: env.NODE_ENV,
      version: API_CONSTANTS.VERSION,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      },
      pid: process.pid,
    };

    return sendSuccess(c, healthData, "Service is healthy");
  })

  // Readiness check - GET /health/ready
  .get("/health/ready", (c) => {
    // Add readiness checks here (database, external services, etc.)
    const readyData = {
      status: "ready",
      timestamp: new Date().toISOString(),
      checks: {
        database: "ok", // TODO: Add actual database check
        // redis: "ok", // TODO: Add Redis check if used
        // external_api: "ok", // TODO: Add external API checks
      },
    };

    return sendSuccess(c, readyData, "Service is ready");
  })

  // Liveness check - GET /health/live
  .get("/health/live", (c) => {
    // Simple liveness check
    return sendSuccess(c, { status: "alive" }, "Service is alive");
  })

  // System metrics - GET /metrics (future)
  .get("/metrics", (c) => {
    // TODO: Add Prometheus metrics or custom metrics
    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      // requests_total: 0, // TODO: Add request counter
      // response_time_avg: 0, // TODO: Add response time metrics
    };

    return sendSuccess(c, metrics, "System metrics");
  });
