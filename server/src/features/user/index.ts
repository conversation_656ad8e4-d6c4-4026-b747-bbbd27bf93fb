import { Hono } from "hono";
import { session } from "./session";
import { security } from "./security";
import { profile } from "./profile";

/**
 * User routes module
 * Combines all user-related routes for protected operations
 * These operations require user to be authenticated
 */
const userRoutes = new Hono()
  // Profile management routes - /api/user/profile, /api/user/
  .route("/", profile)

  // Session management routes - /api/user/sessions/*
  .route("/sessions", session)

  // Security routes - /api/user/security/*
  .route("/security", security);

// Export the combined user routes
export { userRoutes };
