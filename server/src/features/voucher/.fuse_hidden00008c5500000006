import { prisma } from "../../lib/prisma";
import { Prisma } from "@prisma/client";
import {
  type CreateVoucherPayload,
  type UpdateVoucherPayload,
  type GetVouchersQuery,
  type ValidateVoucherPayload,
  type RedeemVoucherPayload,
  type BulkUpdateVoucherPayload,
  type BulkDeleteVoucherPayload,
  type VoucherValidationResult,
  type VoucherRedemptionResult,
  type VoucherStats,
  type Voucher,
} from "shared/dist";

/**
 * Voucher Service
 * Handles all voucher-related business logic and database operations
 */

// Voucher select fields for consistent data structure
export const VOUCHER_SELECT_FIELDS = {
  FULL: {
    id: true,
    name: true,
    description: true,
    code: true,
    type: true,
    value: true,
    minValue: true,
    maxValue: true,
    startDate: true,
    endDate: true,
    isActive: true,
    isRedeemed: true,
  },
} as const;

/**
 * Create a new voucher
 */
export async function createVoucher(
  data: CreateVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<Voucher> {
  // Check if voucher code already exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { code: data.code },
    select: { id: true },
  });

  if (existingVoucher) {
    throw new Error("Voucher code already exists");
  }

  // Check if voucher name already exists
  const existingName = await prismaClient.voucher.findUnique({
    where: { name: data.name },
    select: { id: true },
  });

  if (existingName) {
    throw new Error("Voucher name already exists");
  }

  // Validate date range
  if (new Date(data.endDate) <= new Date(data.startDate)) {
    throw new Error("End date must be after start date");
  }

  // Create voucher
  const voucher = await prismaClient.voucher.create({
    data: {
      name: data.name,
      description: data.description,
      code: data.code.toUpperCase(),
      type: data.type,
      value: data.value,
      minValue: data.minValue,
      maxValue: data.maxValue,
      startDate: new Date(data.startDate),
      endDate: new Date(data.endDate),
      isActive: data.isActive,
      isRedeemed: false, // Always false for new vouchers
    },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher;
}

/**
 * Get voucher by ID
 */
export async function getVoucherById(
  id: string,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<Voucher | null> {
  const voucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher | null;
}

/**
 * Get voucher by code
 */
export async function getVoucherByCode(
  code: string,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<Voucher | null> {
  const voucher = await prismaClient.voucher.findUnique({
    where: { code: code.toUpperCase() },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher | null;
}

/**
 * Get vouchers with pagination and filtering
 */
export async function getVouchers(
  query: GetVouchersQuery,
  prismaClient: Prisma.TransactionClient = prisma
) {
  const {
    page = 1,
    limit = 10,
    search,
    type,
    isActive,
    sortBy = "startDate",
    sortOrder = "desc",
  } = query;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { code: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
    ];
  }

  if (type) {
    where.type = type;
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  // Build order by clause
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get vouchers and total count
  const [vouchers, totalItems] = await Promise.all([
    prismaClient.voucher.findMany({
      where,
      select: VOUCHER_SELECT_FIELDS.FULL,
      skip,
      take: limit,
      orderBy,
    }),
    prismaClient.voucher.count({ where }),
  ]);

  const totalPages = Math.ceil(totalItems / limit);

  return {
    vouchers: vouchers as Voucher[],
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

/**
 * Update voucher
 */
export async function updateVoucher(
  id: string,
  data: UpdateVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<Voucher> {
  // Check if voucher exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: { id: true, code: true, name: true },
  });

  if (!existingVoucher) {
    throw new Error("Voucher not found");
  }

  // Check for duplicate code if code is being updated
  if (data.code && data.code !== existingVoucher.code) {
    const duplicateCode = await prismaClient.voucher.findUnique({
      where: { code: data.code.toUpperCase() },
      select: { id: true },
    });

    if (duplicateCode) {
      throw new Error("Voucher code already exists");
    }
  }

  // Check for duplicate name if name is being updated
  if (data.name && data.name !== existingVoucher.name) {
    const duplicateName = await prismaClient.voucher.findUnique({
      where: { name: data.name },
      select: { id: true },
    });

    if (duplicateName) {
      throw new Error("Voucher name already exists");
    }
  }

  // Validate date range if dates are being updated
  if (data.startDate && data.endDate) {
    if (new Date(data.endDate) <= new Date(data.startDate)) {
      throw new Error("End date must be after start date");
    }
  }

  // Prepare update data
  const updateData: any = {};

  if (data.name) updateData.name = data.name;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.code) updateData.code = data.code.toUpperCase();
  if (data.type) updateData.type = data.type;
  if (data.value) updateData.value = data.value;
  if (data.minValue !== undefined) updateData.minValue = data.minValue;
  if (data.maxValue !== undefined) updateData.maxValue = data.maxValue;
  if (data.startDate) updateData.startDate = new Date(data.startDate);
  if (data.endDate) updateData.endDate = new Date(data.endDate);
  if (data.isActive !== undefined) updateData.isActive = data.isActive;

  // Update voucher
  const voucher = await prismaClient.voucher.update({
    where: { id },
    data: updateData,
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher;
}

/**
 * Delete voucher
 */
export async function deleteVoucher(
  id: string,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<void> {
  // Check if voucher exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: { id: true },
  });

  if (!existingVoucher) {
    throw new Error("Voucher not found");
  }

  // Delete voucher
  await prismaClient.voucher.delete({
    where: { id },
  });
}

/**
 * Bulk update vouchers
 */
export async function bulkUpdateVouchers(
  data: BulkUpdateVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<number> {
  const { ids, data: updateData } = data;

  // Prepare update data
  const prismaUpdateData: any = {};

  if (updateData.name) prismaUpdateData.name = updateData.name;
  if (updateData.description !== undefined)
    prismaUpdateData.description = updateData.description;
  if (updateData.code) prismaUpdateData.code = updateData.code.toUpperCase();
  if (updateData.type) prismaUpdateData.type = updateData.type;
  if (updateData.value) prismaUpdateData.value = updateData.value;
  if (updateData.minValue !== undefined)
    prismaUpdateData.minValue = updateData.minValue;
  if (updateData.maxValue !== undefined)
    prismaUpdateData.maxValue = updateData.maxValue;
  if (updateData.startDate)
    prismaUpdateData.startDate = new Date(updateData.startDate);
  if (updateData.endDate)
    prismaUpdateData.endDate = new Date(updateData.endDate);
  if (updateData.isActive !== undefined)
    prismaUpdateData.isActive = updateData.isActive;

  // Update vouchers
  const result = await prismaClient.voucher.updateMany({
    where: { id: { in: ids } },
    data: prismaUpdateData,
  });

  return result.count;
}

/**
 * Bulk delete vouchers
 */
export async function bulkDeleteVouchers(
  data: BulkDeleteVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<number> {
  const { ids } = data;

  // Delete vouchers
  const result = await prismaClient.voucher.deleteMany({
    where: { id: { in: ids } },
  });

  return result.count;
}

/**
 * Validate voucher for use
 */
export async function validateVoucher(
  data: ValidateVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<VoucherValidationResult> {
  const { code, orderAmount } = data;

  try {
    // Get voucher by code
    const voucher = await getVoucherByCode(code, prismaClient);

    if (!voucher) {
      return {
        isValid: false,
        errors: ["Voucher code not found"],
      };
    }

    // Check if voucher is active
    if (!voucher.isActive) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher is not active"],
      };
    }

    // Check if voucher is already redeemed
    if (voucher.isRedeemed) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher has already been redeemed"],
      };
    }

    // Check if voucher is within valid date range
    const now = new Date();
    const startDate = new Date(voucher.startDate);
    const endDate = new Date(voucher.endDate);

    if (now < startDate) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher is not yet valid"],
      };
    }

    if (now > endDate) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher has expired"],
      };
    }

    // Check minimum order amount if specified
    if (
      orderAmount !== undefined &&
      voucher.minValue &&
      orderAmount < voucher.minValue
    ) {
      return {
        isValid: false,
        voucher,
        errors: [`Minimum order amount is ${voucher.minValue}`],
      };
    }

    // Calculate discount amount
    let discountAmount = 0;
    let finalAmount = orderAmount || 0;

    if (orderAmount !== undefined) {
      if (voucher.type === "PERCENTAGE") {
        discountAmount = (orderAmount * voucher.value) / 100;

        // Apply maximum discount if specified
        if (voucher.maxValue && discountAmount > voucher.maxValue) {
          discountAmount = voucher.maxValue;
        }
      } else {
        // FIXED_AMOUNT
        discountAmount = voucher.value;

        // Discount cannot exceed order amount
        if (discountAmount > orderAmount) {
          discountAmount = orderAmount;
        }
      }

      finalAmount = orderAmount - discountAmount;
    }

    return {
      isValid: true,
      voucher,
      discountAmount,
      finalAmount,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: ["Error validating voucher"],
    };
  }
}

/**
 * Redeem voucher
 */
export async function redeemVoucher(
  data: RedeemVoucherPayload,
  prismaClient: Prisma.TransactionClient = prisma
): Promise<VoucherRedemptionResult> {
  const { code, orderAmount } = data;

  // First validate the voucher
  const validation = await validateVoucher({ code, orderAmount }, prismaClient);

  if (!validation.isValid || !validation.voucher) {
    throw new Error(validation.errors?.[0] || "Invalid voucher");
  }

  // Mark voucher as redeemed
  const redeemedVoucher = await prismaClient.voucher.update({
    where: { id: validation.voucher.id },
    data: { isRedeemed: true },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return {
    success: true,
    voucher: redeemedVoucher as Voucher,
    discountAmount: validation.discountAmount || 0,
    finalAmount: validation.finalAmount || orderAmount,
    originalAmount: orderAmount,
  };
}

/**
 * Get voucher statistics
 */
export async function getVoucherStats(
  prismaClient: Prisma.TransactionClient = prisma
): Promise<VoucherStats> {
  const [totalVouchers, activeVouchers, redeemedVouchers] = await Promise.all([
    prismaClient.voucher.count(),
    prismaClient.voucher.count({
      where: { isActive: true },
    }),
    prismaClient.voucher.count({
      where: { isRedeemed: true },
    }),
  ]);

  // Calculate total discount given (simplified - in real app you'd track this in transactions)
  const redeemedVouchersList = await prismaClient.voucher.findMany({
    where: { isRedeemed: true },
    select: { value: true, type: true },
  });

  const totalDiscountGiven = redeemedVouchersList.reduce(
    (total: number, voucher: any) => {
      // This is a simplified calculation - in real app you'd have actual redemption records
      return total + Number(voucher.value);
    },
    0
  );

  return {
    totalVouchers,
    activeVouchers,
    redeemedVouchers,
    totalDiscountGiven,
  };
}

/**
 * Get active vouchers (not expired, not redeemed, active)
 */
export async function getActiveVouchers(
  query: { page?: number; limit?: number; search?: string },
  prismaClient: Prisma.TransactionClient = prisma
) {
  const { page = 1, limit = 10, search } = query;
  const skip = (page - 1) * limit;
  const now = new Date();

  // Build where clause for active vouchers
  const where: any = {
    isActive: true,
    isRedeemed: false,
    startDate: { lte: now },
    endDate: { gte: now },
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { code: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
    ];
  }

  // Get vouchers and total count
  const [vouchers, totalItems] = await Promise.all([
    prismaClient.voucher.findMany({
      where,
      select: VOUCHER_SELECT_FIELDS.FULL,
      skip,
      take: limit,
      orderBy: { startDate: "desc" },
    }),
    prismaClient.voucher.count({ where }),
  ]);

  const totalPages = Math.ceil(totalItems / limit);

  return {
    vouchers: vouchers as Voucher[],
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}
