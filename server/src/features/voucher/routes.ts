import { Hono } from "hono";
import {
  validateBody,
  validateQuery,
  validateParam,
} from "../../middleware/validation";
import { sendError, sendSuccess } from "../../utils/response";
import {
  createVoucherSchema,
  updateVoucherSchema,
  getVoucherSchema,
  getVoucherByCodeSchema,
  getVouchersSchema,
  bulkUpdateVoucherSchema,
  bulkDeleteVoucherSchema,
  validateVoucherSchema,
  redeemVoucherSchema,
  getActiveVouchersSchema,
  type CreateVoucherPayload,
  type UpdateVoucherPayload,
  type GetVoucherParams,
  type GetVoucherByCodeParams,
  type GetVouchersQuery,
  type BulkUpdateVoucherPayload,
  type BulkDeleteVoucherPayload,
  type ValidateVoucherPayload,
  type RedeemVoucherPayload,
  type GetActiveVouchersQuery,
  type CreateVoucherResponse,
  type GetVoucherResponse,
  type GetVouchersResponse,
  type UpdateVoucherResponse,
  type DeleteVoucherResponse,
  type BulkUpdateVoucherResponse,
  type BulkDeleteVoucherResponse,
  type ValidateVoucherResponse,
  type RedeemVoucherResponse,
  type GetActiveVouchersResponse,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "shared/dist";
import {
  createVoucher,
  getVoucherById,
  getVoucherByCode,
  getVouchers,
  updateVoucher,
  deleteVoucher,
  bulkUpdateVouchers,
  bulkDeleteVouchers,
  validateVoucher,
  redeemVoucher,
  getVoucherStats,
  getActiveVouchers,
} from "./service";

// Create voucher routes
const voucherRoutes = new Hono()

  /**
   * POST /vouchers
   * Create a new voucher
   */
  .post("/", validateBody(createVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as CreateVoucherPayload;

      const voucher = await createVoucher(data);

      const response: CreateVoucherResponse = {
        success: true,
        message: SUCCESS_MESSAGES.CREATED,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 201);
    } catch (error) {
      console.error("Create voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      return sendError(
        c,
        message,
        undefined,
        error instanceof Error && error.message.includes("already exists")
          ? 409
          : 500
      );
    }
  })

  /**
   * GET /vouchers
   * Get vouchers with pagination and filtering
   */
  .get("/", validateQuery(getVouchersSchema), async (c) => {
    try {
      const query = c.req.query() as unknown as GetVouchersQuery;

      const result = await getVouchers(query);

      const response: GetVouchersResponse = {
        success: true,
        message: "Vouchers retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          pagination: result.pagination,
        },
        data: result.vouchers,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /vouchers/active
   * Get active vouchers (not expired, not redeemed)
   */
  .get("/active", validateQuery(getActiveVouchersSchema), async (c) => {
    try {
      const query = c.req.query() as unknown as GetActiveVouchersQuery;

      const result = await getActiveVouchers(query);

      const response: GetActiveVouchersResponse = {
        success: true,
        message: "Active vouchers retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          pagination: result.pagination,
        },
        data: result.vouchers,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get active vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /vouchers/stats
   * Get voucher statistics
   */
  .get("/stats", async (c) => {
    try {
      const stats = await getVoucherStats();

      return sendSuccess(c, stats, "Voucher statistics retrieved successfully");
    } catch (error) {
      console.error("Get voucher stats error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /vouchers/code/:code
   * Get voucher by code
   */
  .get("/code/:code", validateParam(getVoucherByCodeSchema), async (c) => {
    try {
      const { code } = c.req.param() as GetVoucherByCodeParams;

      const voucher = await getVoucherByCode(code);

      if (!voucher) {
        return sendError(c, "Voucher not found", undefined, 404);
      }

      const response: GetVoucherResponse = {
        success: true,
        message: "Voucher retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get voucher by code error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /vouchers/:id
   * Get voucher by ID
   */
  .get("/:id", validateParam(getVoucherSchema), async (c) => {
    try {
      const { id } = c.req.param() as GetVoucherParams;

      const voucher = await getVoucherById(id);

      if (!voucher) {
        return sendError(c, "Voucher not found", undefined, 404);
      }

      const response: GetVoucherResponse = {
        success: true,
        message: "Voucher retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get voucher error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * PUT /vouchers/:id
   * Update voucher
   */
  .put(
    "/:id",
    validateParam(getVoucherSchema),
    validateBody(updateVoucherSchema),
    async (c) => {
      try {
        const { id } = c.req.param() as GetVoucherParams;
        const data = (await c.req.json()) as UpdateVoucherPayload;

        const voucher = await updateVoucher(id, data);

        const response: UpdateVoucherResponse = {
          success: true,
          message: SUCCESS_MESSAGES.UPDATED,
          meta: {
            timestamp: new Date().toISOString(),
            version: "1.0.0",
          },
          data: voucher,
        };

        return c.json(response, 200);
      } catch (error) {
        console.error("Update voucher error:", error);
        const message =
          error instanceof Error
            ? error.message
            : ERROR_MESSAGES.INTERNAL_ERROR;
        let statusCode = 500;
        if (error instanceof Error) {
          if (error.message.includes("not found")) {
            statusCode = 404;
          } else if (error.message.includes("already exists")) {
            statusCode = 409;
          }
        }
        return sendError(c, message, undefined, statusCode);
      }
    }
  )

  /**
   * DELETE /vouchers/:id
   * Delete voucher
   */
  .delete("/:id", validateParam(getVoucherSchema), async (c) => {
    try {
      const { id } = c.req.param() as GetVoucherParams;

      await deleteVoucher(id);

      const response: DeleteVoucherResponse = {
        success: true,
        message: SUCCESS_MESSAGES.DELETED,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: null,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Delete voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 500;
      return sendError(c, message, undefined, statusCode);
    }
  })

  /**
   * PUT /vouchers/bulk
   * Bulk update vouchers
   */
  .put("/bulk", validateBody(bulkUpdateVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as BulkUpdateVoucherPayload;

      const count = await bulkUpdateVouchers(data);

      const response: BulkUpdateVoucherResponse = {
        success: true,
        message: `${count} vouchers updated successfully`,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: { count },
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Bulk update vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * DELETE /vouchers/bulk
   * Bulk delete vouchers
   */
  .delete("/bulk", validateBody(bulkDeleteVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as BulkDeleteVoucherPayload;

      const count = await bulkDeleteVouchers(data);

      const response: BulkDeleteVoucherResponse = {
        success: true,
        message: `${count} vouchers deleted successfully`,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: { count },
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Bulk delete vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * POST /vouchers/validate
   * Validate voucher for use
   */
  .post("/validate", validateBody(validateVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as ValidateVoucherPayload;

      const result = await validateVoucher(data);

      const response: ValidateVoucherResponse = {
        success: true,
        message: result.isValid
          ? "Voucher is valid"
          : "Voucher validation failed",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: result,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Validate voucher error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * POST /vouchers/redeem
   * Redeem voucher
   */
  .post("/redeem", validateBody(redeemVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as RedeemVoucherPayload;

      const result = await redeemVoucher(data);

      const response: RedeemVoucherResponse = {
        success: true,
        message: "Voucher redeemed successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: result,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Redeem voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      return sendError(c, message, undefined, 400);
    }
  });

// Export the voucher routes
export { voucherRoutes };
