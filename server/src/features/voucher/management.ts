import { Hono } from "hono";
import {
  validateBody,
  validateQuery,
  validateParam,
} from "../../middleware/validation";
import { sendError, sendSuccess } from "../../utils/response";
import { prisma } from "../../lib/prisma";
import {
  createVoucherSchema,
  updateVoucherSchema,
  getVoucherSchema,
  getVoucherByCodeSchema,
  getVouchersSchema,
  bulkUpdateVoucherSchema,
  bulkDeleteVoucherSchema,
  type CreateVoucherPayload,
  type UpdateVoucherPayload,
  type GetVoucherParams,
  type GetVoucherByCodeParams,
  type GetVouchersQuery,
  type BulkUpdateVoucherPayload,
  type BulkDeleteVoucherPayload,
  type Voucher,
  type CreateVoucherResponse,
  type GetVoucherResponse,
  type GetVouchersResponse,
  type UpdateVoucherResponse,
  type DeleteVoucherResponse,
  type BulkUpdateVoucherResponse,
  type BulkDeleteVoucherResponse,
  type VoucherStats,
  SUCCESS_MESSAGES,
  ERROR_MESSAGES,
} from "shared/dist";

// Voucher select fields for consistent data structure
export const VOUCHER_SELECT_FIELDS = {
  FULL: {
    id: true,
    name: true,
    description: true,
    code: true,
    type: true,
    value: true,
    minValue: true,
    maxValue: true,
    startDate: true,
    endDate: true,
    isActive: true,
    isRedeemed: true,
  },
} as const;

/**
 * Service function to create a new voucher
 * Can be called from anywhere with optional transaction client
 */
export const createVoucher = async (
  data: CreateVoucherPayload,
  prismaClient: any = prisma
): Promise<Voucher> => {
  // Check if voucher code already exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { code: data.code },
    select: { id: true },
  });

  if (existingVoucher) {
    throw new Error("Voucher code already exists");
  }

  // Check if voucher name already exists
  const existingName = await prismaClient.voucher.findUnique({
    where: { name: data.name },
    select: { id: true },
  });

  if (existingName) {
    throw new Error("Voucher name already exists");
  }

  // Validate date range
  if (new Date(data.endDate) <= new Date(data.startDate)) {
    throw new Error("End date must be after start date");
  }

  // Create voucher
  const voucher = await prismaClient.voucher.create({
    data: {
      name: data.name,
      description: data.description,
      code: data.code.toUpperCase(),
      type: data.type,
      value: data.value,
      minValue: data.minValue,
      maxValue: data.maxValue,
      startDate: new Date(data.startDate),
      endDate: new Date(data.endDate),
      isActive: data.isActive,
      isRedeemed: false, // Always false for new vouchers
    },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher;
};

/**
 * Service function to get voucher by ID
 */
export const getVoucherById = async (
  id: string,
  prismaClient: any = prisma
): Promise<Voucher | null> => {
  const voucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher | null;
};

/**
 * Service function to get voucher by code
 */
export const getVoucherByCode = async (
  code: string,
  prismaClient: any = prisma
): Promise<Voucher | null> => {
  const voucher = await prismaClient.voucher.findUnique({
    where: { code: code.toUpperCase() },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher | null;
};

/**
 * Service function to get vouchers with pagination and filtering
 */
export const getVouchers = async (
  query: GetVouchersQuery,
  prismaClient: any = prisma
) => {
  const {
    page = 1,
    limit = 10,
    search,
    type,
    isActive,
    sortBy = "startDate",
    sortOrder = "desc",
  } = query;

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { code: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
    ];
  }

  if (type) {
    where.type = type;
  }

  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  // Build order by clause
  const orderBy: any = {};
  orderBy[sortBy] = sortOrder;

  // Get vouchers and total count
  const [vouchers, totalItems] = await Promise.all([
    prismaClient.voucher.findMany({
      where,
      select: VOUCHER_SELECT_FIELDS.FULL,
      skip,
      take: limit,
      orderBy,
    }),
    prismaClient.voucher.count({ where }),
  ]);

  const totalPages = Math.ceil(totalItems / limit);

  return {
    vouchers: vouchers as Voucher[],
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

/**
 * Service function to update voucher
 */
export const updateVoucher = async (
  id: string,
  data: UpdateVoucherPayload,
  prismaClient: any = prisma
): Promise<Voucher> => {
  // Check if voucher exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: { id: true, code: true, name: true },
  });

  if (!existingVoucher) {
    throw new Error("Voucher not found");
  }

  // Check for duplicate code if code is being updated
  if (data.code && data.code !== existingVoucher.code) {
    const duplicateCode = await prismaClient.voucher.findUnique({
      where: { code: data.code.toUpperCase() },
      select: { id: true },
    });

    if (duplicateCode) {
      throw new Error("Voucher code already exists");
    }
  }

  // Check for duplicate name if name is being updated
  if (data.name && data.name !== existingVoucher.name) {
    const duplicateName = await prismaClient.voucher.findUnique({
      where: { name: data.name },
      select: { id: true },
    });

    if (duplicateName) {
      throw new Error("Voucher name already exists");
    }
  }

  // Validate date range if dates are being updated
  if (data.startDate && data.endDate) {
    if (new Date(data.endDate) <= new Date(data.startDate)) {
      throw new Error("End date must be after start date");
    }
  }

  // Prepare update data
  const updateData: any = {};

  if (data.name) updateData.name = data.name;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.code) updateData.code = data.code.toUpperCase();
  if (data.type) updateData.type = data.type;
  if (data.value) updateData.value = data.value;
  if (data.minValue !== undefined) updateData.minValue = data.minValue;
  if (data.maxValue !== undefined) updateData.maxValue = data.maxValue;
  if (data.startDate) updateData.startDate = new Date(data.startDate);
  if (data.endDate) updateData.endDate = new Date(data.endDate);
  if (data.isActive !== undefined) updateData.isActive = data.isActive;

  // Update voucher
  const voucher = await prismaClient.voucher.update({
    where: { id },
    data: updateData,
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return voucher as Voucher;
};

/**
 * Service function to delete voucher
 */
export const deleteVoucher = async (
  id: string,
  prismaClient: any = prisma
): Promise<void> => {
  // Check if voucher exists
  const existingVoucher = await prismaClient.voucher.findUnique({
    where: { id },
    select: { id: true },
  });

  if (!existingVoucher) {
    throw new Error("Voucher not found");
  }

  // Delete voucher
  await prismaClient.voucher.delete({
    where: { id },
  });
};

/**
 * Service function to bulk update vouchers
 */
export const bulkUpdateVouchers = async (
  data: BulkUpdateVoucherPayload,
  prismaClient: any = prisma
): Promise<number> => {
  const { ids, data: updateData } = data;

  // Prepare update data
  const prismaUpdateData: any = {};

  if (updateData.name) prismaUpdateData.name = updateData.name;
  if (updateData.description !== undefined)
    prismaUpdateData.description = updateData.description;
  if (updateData.code) prismaUpdateData.code = updateData.code.toUpperCase();
  if (updateData.type) prismaUpdateData.type = updateData.type;
  if (updateData.value) prismaUpdateData.value = updateData.value;
  if (updateData.minValue !== undefined)
    prismaUpdateData.minValue = updateData.minValue;
  if (updateData.maxValue !== undefined)
    prismaUpdateData.maxValue = updateData.maxValue;
  if (updateData.startDate)
    prismaUpdateData.startDate = new Date(updateData.startDate);
  if (updateData.endDate)
    prismaUpdateData.endDate = new Date(updateData.endDate);
  if (updateData.isActive !== undefined)
    prismaUpdateData.isActive = updateData.isActive;

  // Update vouchers
  const result = await prismaClient.voucher.updateMany({
    where: { id: { in: ids } },
    data: prismaUpdateData,
  });

  return result.count;
};

/**
 * Service function to bulk delete vouchers
 */
export const bulkDeleteVouchers = async (
  data: BulkDeleteVoucherPayload,
  prismaClient: any = prisma
): Promise<number> => {
  const { ids } = data;

  // Delete vouchers
  const result = await prismaClient.voucher.deleteMany({
    where: { id: { in: ids } },
  });

  return result.count;
};

/**
 * Service function to get voucher statistics
 */
export const getVoucherStats = async (
  prismaClient: any = prisma
): Promise<VoucherStats> => {
  const [totalVouchers, activeVouchers, redeemedVouchers] = await Promise.all([
    prismaClient.voucher.count(),
    prismaClient.voucher.count({
      where: { isActive: true },
    }),
    prismaClient.voucher.count({
      where: { isRedeemed: true },
    }),
  ]);

  // Calculate total discount given (simplified - in real app you'd track this in transactions)
  const redeemedVouchersList = await prismaClient.voucher.findMany({
    where: { isRedeemed: true },
    select: { value: true, type: true },
  });

  const totalDiscountGiven = redeemedVouchersList.reduce(
    (total: number, voucher: any) => {
      // This is a simplified calculation - in real app you'd have actual redemption records
      return total + Number(voucher.value);
    },
    0
  );

  return {
    totalVouchers,
    activeVouchers,
    redeemedVouchers,
    totalDiscountGiven,
  };
};

// Create voucher management routes
const voucherManagement = new Hono()

  /**
   * POST /
   * Create a new voucher
   */
  .post("/", validateBody(createVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as CreateVoucherPayload;

      const voucher = await createVoucher(data);

      const response: CreateVoucherResponse = {
        success: true,
        message: SUCCESS_MESSAGES.CREATED,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 201);
    } catch (error) {
      console.error("Create voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      let statusCode = 500;
      if (error instanceof Error && error.message.includes("already exists")) {
        statusCode = 409;
      }
      return sendError(c, message, undefined, statusCode);
    }
  })

  /**
   * GET /
   * Get vouchers with pagination and filtering
   */
  .get("/", validateQuery(getVouchersSchema), async (c) => {
    try {
      const query = c.req.query() as unknown as GetVouchersQuery;

      const result = await getVouchers(query);

      const response: GetVouchersResponse = {
        success: true,
        message: "Vouchers retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          pagination: result.pagination,
        },
        data: result.vouchers,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /stats
   * Get voucher statistics
   */
  .get("/stats", async (c) => {
    try {
      const stats = await getVoucherStats();

      return sendSuccess(c, stats, "Voucher statistics retrieved successfully");
    } catch (error) {
      console.error("Get voucher stats error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /code/:code
   * Get voucher by code
   */
  .get("/code/:code", validateParam(getVoucherByCodeSchema), async (c) => {
    try {
      const { code } = c.req.param() as GetVoucherByCodeParams;

      const voucher = await getVoucherByCode(code);

      if (!voucher) {
        return sendError(c, "Voucher not found", undefined, 404);
      }

      const response: GetVoucherResponse = {
        success: true,
        message: "Voucher retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get voucher by code error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * GET /:id
   * Get voucher by ID
   */
  .get("/:id", validateParam(getVoucherSchema), async (c) => {
    try {
      const { id } = c.req.param() as GetVoucherParams;

      const voucher = await getVoucherById(id);

      if (!voucher) {
        return sendError(c, "Voucher not found", undefined, 404);
      }

      const response: GetVoucherResponse = {
        success: true,
        message: "Voucher retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: voucher,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get voucher error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * PUT /:id
   * Update voucher
   */
  .put(
    "/:id",
    validateParam(getVoucherSchema),
    validateBody(updateVoucherSchema),
    async (c) => {
      try {
        const { id } = c.req.param() as GetVoucherParams;
        const data = (await c.req.json()) as UpdateVoucherPayload;

        const voucher = await updateVoucher(id, data);

        const response: UpdateVoucherResponse = {
          success: true,
          message: SUCCESS_MESSAGES.UPDATED,
          meta: {
            timestamp: new Date().toISOString(),
            version: "1.0.0",
          },
          data: voucher,
        };

        return c.json(response, 200);
      } catch (error) {
        console.error("Update voucher error:", error);
        const message =
          error instanceof Error
            ? error.message
            : ERROR_MESSAGES.INTERNAL_ERROR;
        let statusCode = 500;
        if (error instanceof Error) {
          if (error.message.includes("not found")) {
            statusCode = 404;
          } else if (error.message.includes("already exists")) {
            statusCode = 409;
          }
        }
        return sendError(c, message, undefined, statusCode);
      }
    }
  )

  /**
   * DELETE /:id
   * Delete voucher
   */
  .delete("/:id", validateParam(getVoucherSchema), async (c) => {
    try {
      const { id } = c.req.param() as GetVoucherParams;

      await deleteVoucher(id);

      const response: DeleteVoucherResponse = {
        success: true,
        message: SUCCESS_MESSAGES.DELETED,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: null,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Delete voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      const statusCode =
        error instanceof Error && error.message.includes("not found")
          ? 404
          : 500;
      return sendError(c, message, undefined, statusCode);
    }
  })

  /**
   * PUT /bulk
   * Bulk update vouchers
   */
  .put("/bulk", validateBody(bulkUpdateVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as BulkUpdateVoucherPayload;

      const count = await bulkUpdateVouchers(data);

      const response: BulkUpdateVoucherResponse = {
        success: true,
        message: `${count} vouchers updated successfully`,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: { count },
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Bulk update vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * DELETE /bulk
   * Bulk delete vouchers
   */
  .delete("/bulk", validateBody(bulkDeleteVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as BulkDeleteVoucherPayload;

      const count = await bulkDeleteVouchers(data);

      const response: BulkDeleteVoucherResponse = {
        success: true,
        message: `${count} vouchers deleted successfully`,
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: { count },
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Bulk delete vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  });

// Export the voucher management routes
export { voucherManagement };
