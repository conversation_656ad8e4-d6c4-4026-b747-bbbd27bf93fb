import { Hono } from "hono";
import { voucherManagement } from "./management";
import { voucherValidation } from "./validation";

/**
 * Voucher routes module
 * Combines all voucher-related routes with proper Hono chaining for RPC
 * Note: Order matters! More specific routes (like /active) must come before generic routes (like /:id)
 */
const voucher = new Hono()
  // Validation routes first - /api/voucher/* (validation and redemption)
  // These include specific routes like /active, /validate, /redeem
  .route("/", voucherValidation)

  // Management routes second - /api/voucher/* (CRUD operations)
  // These include generic routes like /:id which should come last
  .route("/", voucherManagement);

// Export the combined voucher routes
export { voucher };

// Export individual modules for direct access if needed
export { voucherManagement } from "./management";
export { voucherValidation } from "./validation";

// Export service functions for use in other modules
export {
  createVoucher,
  getVoucherById,
  getVoucherByCode,
  getVouchers,
  updateVoucher,
  deleteVoucher,
  bulkUpdateVouchers,
  bulkDeleteVouchers,
  getVoucherStats,
  VOUCHER_SELECT_FIELDS,
} from "./management";

export {
  validateVoucher,
  redeemVoucher,
  getActiveVouchers,
} from "./validation";
