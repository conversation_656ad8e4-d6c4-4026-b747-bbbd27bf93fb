import { Hono } from "hono";
import { validateBody, validateQuery } from "../../middleware/validation";
import { sendError } from "../../utils/response";
import { prisma } from "../../lib/prisma";
import {
  validateVoucherSchema,
  redeemVoucherSchema,
  getActiveVouchersSchema,
  type ValidateVoucherPayload,
  type RedeemVoucherPayload,
  type GetActiveVouchersQuery,
  type VoucherValidationResult,
  type VoucherRedemptionResult,
  type ValidateVoucherResponse,
  type RedeemVoucherResponse,
  type GetActiveVouchersResponse,
  type Voucher,
  ERROR_MESSAGES,
} from "shared/dist";
import { getVoucherByCode, VOUCHER_SELECT_FIELDS } from "./management";

/**
 * Service function to validate voucher for use
 * Can be called from anywhere with optional transaction client
 */
export const validateVoucher = async (
  data: ValidateVoucherPayload,
  prismaClient: any = prisma
): Promise<VoucherValidationResult> => {
  const { code, orderAmount } = data;

  try {
    // Get voucher by code
    const voucher = await getVoucherByCode(code, prismaClient);

    if (!voucher) {
      return {
        isValid: false,
        errors: ["Voucher code not found"],
      };
    }

    // Check if voucher is active
    if (!voucher.isActive) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher is not active"],
      };
    }

    // Check if voucher is already redeemed
    if (voucher.isRedeemed) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher has already been redeemed"],
      };
    }

    // Check if voucher is within valid date range
    const now = new Date();
    const startDate = new Date(voucher.startDate);
    const endDate = new Date(voucher.endDate);

    if (now < startDate) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher is not yet valid"],
      };
    }

    if (now > endDate) {
      return {
        isValid: false,
        voucher,
        errors: ["Voucher has expired"],
      };
    }

    // Check minimum order amount if specified
    if (
      orderAmount !== undefined &&
      voucher.minValue &&
      orderAmount < voucher.minValue
    ) {
      return {
        isValid: false,
        voucher,
        errors: [`Minimum order amount is ${voucher.minValue}`],
      };
    }

    // Calculate discount amount
    let discountAmount = 0;
    let finalAmount = orderAmount || 0;

    if (orderAmount !== undefined) {
      if (voucher.type === "PERCENTAGE") {
        discountAmount = (orderAmount * voucher.value) / 100;

        // Apply maximum discount if specified
        if (voucher.maxValue && discountAmount > voucher.maxValue) {
          discountAmount = voucher.maxValue;
        }
      } else {
        // FIXED_AMOUNT
        discountAmount = voucher.value;

        // Discount cannot exceed order amount
        if (discountAmount > orderAmount) {
          discountAmount = orderAmount;
        }
      }

      finalAmount = orderAmount - discountAmount;
    }

    return {
      isValid: true,
      voucher,
      discountAmount,
      finalAmount,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: ["Error validating voucher"],
    };
  }
};

/**
 * Service function to redeem voucher
 * Can be called from anywhere with optional transaction client
 */
export const redeemVoucher = async (
  data: RedeemVoucherPayload,
  prismaClient: any = prisma
): Promise<VoucherRedemptionResult> => {
  const { code, orderAmount } = data;

  // First validate the voucher
  const validation = await validateVoucher({ code, orderAmount }, prismaClient);

  if (!validation.isValid || !validation.voucher) {
    throw new Error(validation.errors?.[0] || "Invalid voucher");
  }

  // Mark voucher as redeemed
  const redeemedVoucher = await prismaClient.voucher.update({
    where: { id: validation.voucher.id },
    data: { isRedeemed: true },
    select: VOUCHER_SELECT_FIELDS.FULL,
  });

  return {
    success: true,
    voucher: redeemedVoucher as Voucher,
    discountAmount: validation.discountAmount || 0,
    finalAmount: validation.finalAmount || orderAmount,
    originalAmount: orderAmount,
  };
};

/**
 * Service function to get active vouchers (not expired, not redeemed, active)
 * Can be called from anywhere with optional transaction client
 */
export const getActiveVouchers = async (
  query: { page?: number; limit?: number; search?: string },
  prismaClient: any = prisma
) => {
  const { page = 1, limit = 10, search } = query;
  const skip = (page - 1) * limit;
  const now = new Date();

  // Build where clause for active vouchers
  const where: any = {
    isActive: true,
    isRedeemed: false,
    startDate: { lte: now },
    endDate: { gte: now },
  };

  if (search) {
    where.OR = [
      { name: { contains: search, mode: "insensitive" } },
      { code: { contains: search, mode: "insensitive" } },
      { description: { contains: search, mode: "insensitive" } },
    ];
  }

  // Get vouchers and total count
  const [vouchers, totalItems] = await Promise.all([
    prismaClient.voucher.findMany({
      where,
      select: VOUCHER_SELECT_FIELDS.FULL,
      skip,
      take: limit,
      orderBy: { startDate: "desc" },
    }),
    prismaClient.voucher.count({ where }),
  ]);

  const totalPages = Math.ceil(totalItems / limit);

  return {
    vouchers: vouchers as Voucher[],
    pagination: {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

// Create voucher validation routes
const voucherValidation = new Hono()

  /**
   * GET /active
   * Get active vouchers (not expired, not redeemed)
   */
  .get("/active", validateQuery(getActiveVouchersSchema), async (c) => {
    try {
      const query = c.req.query() as unknown as GetActiveVouchersQuery;

      const result = await getActiveVouchers(query);

      const response: GetActiveVouchersResponse = {
        success: true,
        message: "Active vouchers retrieved successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
          pagination: result.pagination,
        },
        data: result.vouchers,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Get active vouchers error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * POST /validate
   * Validate voucher for use
   */
  .post("/validate", validateBody(validateVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as ValidateVoucherPayload;

      const result = await validateVoucher(data);

      const response: ValidateVoucherResponse = {
        success: true,
        message: result.isValid
          ? "Voucher is valid"
          : "Voucher validation failed",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: result,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Validate voucher error:", error);
      return sendError(c, ERROR_MESSAGES.INTERNAL_ERROR, undefined, 500);
    }
  })

  /**
   * POST /redeem
   * Redeem voucher
   */
  .post("/redeem", validateBody(redeemVoucherSchema), async (c) => {
    try {
      const data = (await c.req.json()) as RedeemVoucherPayload;

      const result = await redeemVoucher(data);

      const response: RedeemVoucherResponse = {
        success: true,
        message: "Voucher redeemed successfully",
        meta: {
          timestamp: new Date().toISOString(),
          version: "1.0.0",
        },
        data: result,
      };

      return c.json(response, 200);
    } catch (error) {
      console.error("Redeem voucher error:", error);
      const message =
        error instanceof Error ? error.message : ERROR_MESSAGES.INTERNAL_ERROR;
      return sendError(c, message, undefined, 400);
    }
  });

// Export the voucher validation routes
export { voucherValidation };
